const { groq } = require("@ai-sdk/groq");
const { google } = require("@ai-sdk/google");
const {
  getNodeAutoInstrumentations,
} = require("@opentelemetry/auto-instrumentations-node");
const { NodeSDK } = require("@opentelemetry/sdk-node");
const {
  generateText: aiGenerateText,
  experimental_createMCPClient: createMCPClient,
} = require("ai");
const { LangfuseExporter } = require("langfuse-vercel");
const { configService } = require("./configService");
const logger = require("../utils/logger");

// Initialize SDK once at module load time
const sdk = new NodeSDK({
  traceExporter: new LangfuseExporter(),
  instrumentations: [getNodeAutoInstrumentations()],
});

// Start the SDK once
sdk.start();

// Handle graceful shutdown
process.on("SIGTERM", async () => {
  await sdk.shutdown();
});

process.on("SIGINT", async () => {
  await sdk.shutdown();
});

async function generateText(messages) {
  // Get current configuration
  const provider = configService.get("provider");
  const model = configService.get("model");
  const temperature = configService.get("temperature");
  let mcpClient;

  try {
    logger.info("Creating MCP client...");
    mcpClient = await createMCPClient({
      transport: {
        type: "sse",
        url: "https://mcp.supermemory.ai/6Wy3fW0xlIJJTSIKN7C9H/sse",
      },
    });
    logger.success("MCP client created successfully");

    logger.info("Fetching MCP tools...");
    const tools = await mcpClient.tools();
    logger.info(JSON.stringify(tools));

    // Build the config object
    const config = {
      model:
        provider === "groq"
          ? groq(model)
          : google(model, {
              useSearchGrounding: true,
              dynamicRetrievalConfig: {
                mode: "MODE_DYNAMIC",
                dynamicThreshold: 0.8,
              },
            }),
      messages: messages,
      tools,
      maxSteps: 5,
      temperature: temperature,
      // providerOptions: {
      //   google: { responseModalities: ["TEXT", "IMAGE"] },
      // },
      thinkingConfig: {
        thinkingBudget: 0,
      },
      experimental_telemetry: {
        isEnabled: true,
        functionId: "generateText",
      },
    };

    logger.info(
      `Config prepared - Provider: ${provider}, Model: ${model}, MaxSteps: ${config.maxSteps}`
    );

    logger.info("Calling AI generateText with tools...");
    const result = await aiGenerateText(config);

    logger.info("AI generation completed");
    logger.info(`Tool calls made:`);
    logger.info(JSON.stringify(result.steps));
    const allToolCalls = result.steps.flatMap((step) => step.toolCalls);
    logger.info(JSON.stringify(allToolCalls));

    return result.text;
  } catch (error) {
    logger.error("Error in generateText:", error);
    throw error;
  } finally {
    if (mcpClient) {
      logger.info("Closing MCP client...");
      await mcpClient.close();
    }
  }
}

module.exports = {
  generateText,
};
